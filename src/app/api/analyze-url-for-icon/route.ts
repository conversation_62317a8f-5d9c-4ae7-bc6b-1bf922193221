import { NextRequest, NextResponse } from 'next/server';
import { extractColorsWithCSS } from '@/utils/websiteAnalyzer';

interface AnalyzeUrlRequest {
  url: string;
}

interface AnalyzeUrlResponse {
  success: boolean;
  prompt?: string;
  websiteInfo?: {
    title?: string;
    description?: string;
    siteName?: string;
    extractedColors?: string[];
  };
  error?: string;
}

export async function POST(request: NextRequest) {
  try {
    const body: AnalyzeUrlRequest = await request.json();
    const { url } = body;

    if (!url || url.trim() === '') {
      return NextResponse.json<AnalyzeUrlResponse>(
        { success: false, error: 'Please provide a valid URL.' },
        { status: 400 }
      );
    }

    // Normalize URL
    let normalizedUrl = url.trim();
    if (!normalizedUrl.startsWith('http://') && !normalizedUrl.startsWith('https://')) {
      normalizedUrl = 'https://' + normalizedUrl;
    }

    // Get API key and model from environment variables
    const apiKey = process.env.GOOGLE_AI_API_KEY;
    const geminiModel = process.env.GEMINI_MODEL || 'gemini-2.5-flash-preview-05-20';
    
    if (!apiKey) {
      return NextResponse.json<AnalyzeUrlResponse>(
        { success: false, error: 'API key not configured. Please set GOOGLE_AI_API_KEY environment variable.' },
        { status: 500 }
      );
    }

    // Fetch the website content
    let websiteContent = '';
    let extractedColors: string[] = [];
    let websiteInfo: {
      title?: string;
      description?: string;
      siteName?: string;
      extractedColors?: string[];
    } = {};
    
    try {
      const response = await fetch(normalizedUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (compatible; IconGenerator/1.0; +https://example.com/bot)',
        },
        signal: AbortSignal.timeout(10000), // 10 second timeout
      });

      if (response.ok) {
        const html = await response.text();
        
        // Extract basic info for display
        const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
        const descMatch = html.match(/<meta[^>]*name="description"[^>]*content="([^"]*)"[^>]*>/i);
        const ogTitleMatch = html.match(/<meta[^>]*property="og:title"[^>]*content="([^"]*)"[^>]*>/i);
        const ogSiteMatch = html.match(/<meta[^>]*property="og:site_name"[^>]*content="([^"]*)"[^>]*>/i);
        
        // Extract colors from the HTML and external CSS
        extractedColors = await extractColorsWithCSS(html, normalizedUrl);
        console.log('🎨 Color extraction results for', normalizedUrl);
        console.log('📊 Number of colors found:', extractedColors.length);
        console.log('🌈 Extracted colors:', extractedColors);

        websiteInfo = {
          title: titleMatch?.[1] || ogTitleMatch?.[1] || '',
          description: descMatch?.[1] || '',
          siteName: ogSiteMatch?.[1] || '',
          extractedColors
        };

        // Clean HTML for AI analysis
        websiteContent = html
          .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
          .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '')
          .replace(/<[^>]*>/g, ' ')
          .replace(/\s+/g, ' ')
          .trim()
          .slice(0, 10000); // Limit content for API efficiency
      }
    } catch (fetchError) {
      console.warn('Could not fetch website content:', fetchError);
      // Continue with URL-only analysis
    }

    // Create AI prompt for icon generation
    console.log('🤖 Preparing AI prompt with colors:', extractedColors);

    const analysisPrompt = `Analyze this website and create a detailed prompt for generating a professional app icon.

Website URL: ${normalizedUrl}
${websiteInfo.title ? `Website Title: ${websiteInfo.title}` : ''}
${websiteInfo.description ? `Website Description: ${websiteInfo.description}` : ''}
${websiteInfo.siteName ? `Site Name: ${websiteInfo.siteName}` : ''}
${extractedColors.length > 0 ? `Website Brand Colors: ${extractedColors.join(', ')}` : 'No brand colors detected'}

${websiteContent ? `Website Content (first 10,000 characters):\n${websiteContent}` : 'Website content could not be accessed - please analyze based on URL and any available metadata.'}

Based on your analysis of this website, create a comprehensive prompt for an AI image generator to create a professional app icon. The prompt should include:

1. The website's purpose and industry
2. Appropriate visual elements and symbols
3. Color scheme - IMPORTANT: If Website Brand Colors are provided above, use those EXACT hex color values in your prompt. Do not guess or interpret colors - use the extracted hex values exactly as provided.
4. Design style (modern, minimalist, corporate, etc.)
5. Technical requirements for app store submission

CRITICAL COLOR INSTRUCTION: If brand colors were extracted from the website (listed above), you MUST include those exact hex color values in your icon generation prompt. For example, if the extracted colors are "#3b82f6, #10b981", your prompt should specify "using primary color #3b82f6, secondary color #10b981" rather than describing colors in general terms.

Create a detailed, specific prompt that would generate an icon that perfectly represents this website. Focus on accuracy and brand appropriateness, especially regarding the exact color values.

Respond with ONLY the icon generation prompt, no additional text or explanation.`;

    const chatHistory = [{
      role: "user",
      parts: [{ text: analysisPrompt }]
    }];

    const payload = { contents: chatHistory };
    const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${geminiModel}:generateContent?key=${apiKey}`;

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error?.message || 'AI analysis failed.');
    }

    const result = await response.json();

    if (result.candidates && result.candidates.length > 0 &&
        result.candidates[0].content && result.candidates[0].content.parts &&
        result.candidates[0].content.parts.length > 0) {

      const generatedPrompt = result.candidates[0].content.parts[0].text.trim();

      console.log('✅ AI generated prompt:');
      console.log(generatedPrompt);
      console.log('🎨 Colors in final response:', extractedColors);

      return NextResponse.json<AnalyzeUrlResponse>({
        success: true,
        prompt: generatedPrompt,
        websiteInfo
      });
    } else {
      return NextResponse.json<AnalyzeUrlResponse>(
        { success: false, error: 'Could not generate icon prompt from website analysis.' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error analyzing URL for icon:', error);
    
    let errorMessage = 'Failed to analyze website';
    if (error instanceof Error) {
      if (error.message.includes('fetch')) {
        errorMessage = 'Unable to access the website. Please check the URL and try again.';
      } else if (error.message.includes('timeout')) {
        errorMessage = 'Website took too long to respond. Please try again.';
      } else {
        errorMessage = `Analysis failed: ${error.message}`;
      }
    }

    return NextResponse.json<AnalyzeUrlResponse>(
      { success: false, error: errorMessage },
      { status: 500 }
    );
  }
}
